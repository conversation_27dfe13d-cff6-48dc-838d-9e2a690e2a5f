package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.*;
import com.Bone.BoneSys.entity.enums.ProcessStatus;
import com.Bone.BoneSys.entity.enums.TreatmentMode;
import com.Bone.BoneSys.repository.*;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 进程管理控制器
 * 对应UI页面：新版-进程管理.png, 新版-治疗进程-本地治疗.png, 新版-治疗进程-取走治疗.png
 */
@RestController
@RequestMapping("/api/processes")
@CrossOrigin(origins = "*")
public class ProcessController {

    private static final Logger logger = LoggerFactory.getLogger(ProcessController.class);

    @Autowired
    private ProcessRepository processRepository;
    
    @Autowired
    private TreatmentDetailRepository treatmentDetailRepository;

    /**
     * 获取进程管理页面数据
     * GET /api/processes
     */
    @GetMapping
    public ApiResponse<ProcessListResponse> getProcesses(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String cardId,
            @RequestParam(required = false) String patientName,
            @RequestParam(required = false) String status) {
        
        try {
            logger.info("Fetching processes list - page: {}, size: {}, cardId: {}, patientName: {}, status: {}", 
                       page, size, cardId, patientName, status);

            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("startTime").descending());
            Page<com.Bone.BoneSys.entity.Process> processPage;

            // 根据筛选条件查询
            if (cardId != null && !cardId.trim().isEmpty()) {
                processPage = processRepository.findByRecordPatientPatientCardIdContaining(cardId.trim(), pageable);
            } else if (patientName != null && !patientName.trim().isEmpty()) {
                processPage = processRepository.findByRecordPatientNameContaining(patientName.trim(), pageable);
            } else if (status != null && !status.trim().isEmpty()) {
                ProcessStatus processStatus = ProcessStatus.valueOf(status.trim().toUpperCase());
                processPage = processRepository.findByStatus(processStatus, pageable);
            } else {
                processPage = processRepository.findAll(pageable);
            }

            // 转换为响应格式
            List<ProcessItem> processes = processPage.getContent().stream()
                .map(this::convertToProcessItem)
                .collect(Collectors.toList());

            ProcessListResponse response = new ProcessListResponse();
            response.setProcesses(processes);
            response.setPagination(new PaginationInfo(
                page,
                processPage.getTotalPages(),
                (int) processPage.getTotalElements(),
                size
            ));
            response.setStatusOptions(Arrays.asList("TREATING", "COMPLETED", "AWAITING_RETURN"));

            return ApiResponse.success("进程列表获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching processes list", e);
            return ApiResponse.error(500, "获取进程列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗进程实时数据
     * GET /api/processes/{processId}/realtime
     */
    @GetMapping("/{processId}/realtime")
    public ApiResponse<ProcessRealtimeResponse> getProcessRealtime(@PathVariable Long processId) {
        try {
            logger.info("Fetching process realtime data: {}", processId);

            Optional<com.Bone.BoneSys.entity.Process> processOpt = processRepository.findById(processId);
            if (!processOpt.isPresent()) {
                return ApiResponse.error(404, "进程不存在");
            }

            com.Bone.BoneSys.entity.Process process = processOpt.get();
            
            // 查询治疗详情
            List<TreatmentDetail> treatmentDetails = treatmentDetailRepository.findByProcessId(processId);

            ProcessRealtimeResponse response = new ProcessRealtimeResponse();
            response.setPatientName(process.getRecord().getPatient().getName());
            response.setTreatmentMode(process.getTreatmentMode().name());
            response.setStartTime(process.getStartTime()); // 设置进程开始时间

            // 构建部位治疗信息
            List<BodyPartTreatment> bodyParts = process.getTreatmentDetails().stream()
                .map(detail -> {
                    BodyPartTreatment bodyPart = new BodyPartTreatment();
                    bodyPart.setBodyPart(detail.getBodyPart());
                    bodyPart.setIntensity(detail.getIntensity() + "mW/cm²");
                    
                    // 计算剩余时间和已用时间
                    if (process.getStatus() == ProcessStatus.IN_PROGRESS) {
                        LocalDateTime now = LocalDateTime.now();
                        LocalDateTime startTime = process.getStartTime();
                        long elapsedSeconds = Duration.between(startTime, now).getSeconds();
                        long totalDurationSeconds = detail.getDuration() * 60L; // 转换为秒
                        long remainingSeconds = Math.max(0, totalDurationSeconds - elapsedSeconds);
                        
                        // 精确到秒的剩余时间计算
                        bodyPart.setRemainingTime(formatRemainingTimeWithSeconds(remainingSeconds));
                        bodyPart.setElapsedTime(formatElapsedTime(elapsedSeconds));
                        bodyPart.setElapsedTimeSeconds(elapsedSeconds);
                        bodyPart.setTotalDurationSeconds(totalDurationSeconds);
                        bodyPart.setRemainingTimeSeconds(remainingSeconds); // 添加剩余时间秒数
                    } else {
                        long totalDurationSeconds = detail.getDuration() * 60L;
                        bodyPart.setRemainingTime("已完成");
                        bodyPart.setElapsedTime(formatElapsedTime(totalDurationSeconds));
                        bodyPart.setElapsedTimeSeconds(totalDurationSeconds);
                        bodyPart.setTotalDurationSeconds(totalDurationSeconds);
                        bodyPart.setRemainingTimeSeconds(0L);
                    }
                    
                    return bodyPart;
                })
                .collect(Collectors.toList());

            response.setBodyParts(bodyParts);

            return ApiResponse.success("实时数据获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching process realtime data: {}", processId, e);
            return ApiResponse.error(500, "获取实时数据失败: " + e.getMessage());
        }
    }

    /**
     * 转换Process为ProcessItem
     */
    private ProcessItem convertToProcessItem(com.Bone.BoneSys.entity.Process process) {
        ProcessItem item = new ProcessItem();
        
        item.setProcessId(process.getId());
        item.setCardId(process.getRecord().getPatient().getPatientCardId());
        item.setPatientName(process.getRecord().getPatient().getName());
        
        // 获取治疗部位（取第一个治疗详情的部位）
        List<TreatmentDetail> details = treatmentDetailRepository.findByProcessId(process.getId());
        if (!details.isEmpty()) {
            item.setBodyPart(details.get(0).getBodyPart());
        } else {
            item.setBodyPart("未知");
        }
        
        item.setStatus(convertStatusToDisplayText(process.getStatus()));
        
        return item;
    }

    /**
     * 转换状态为显示文本
     */
    private String convertStatusToDisplayText(ProcessStatus status) {
        switch (status) {
            case IN_PROGRESS:
                return "TREATING";
            case COMPLETED:
                return "COMPLETED";
            case CANCELLED:
                return "CANCELLED";
            default:
                return "UNKNOWN";
        }
    }

    /**
     * 格式化剩余时间（精确到秒）
     */
    private String formatRemainingTimeWithSeconds(long totalSeconds) {
        if (totalSeconds <= 0) {
            return "0分0秒";
        }
        
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;
        
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }

    /**
     * 格式化已用时间（精确到秒）
     */
    private String formatElapsedTime(long totalSeconds) {
        if (totalSeconds < 60) {
            return totalSeconds + "秒";
        }
        
        long minutes = totalSeconds / 60;
        long seconds = totalSeconds % 60;
        
        if (minutes < 60) {
            return minutes + "分" + seconds + "秒";
        } else {
            long hours = minutes / 60;
            long remainingMinutes = minutes % 60;
            return hours + "时" + remainingMinutes + "分" + seconds + "秒";
        }
    }

    // DTO类定义
    @Data
    public static class ProcessListResponse {
        private List<ProcessItem> processes;
        private PaginationInfo pagination;
        private List<String> statusOptions;
    }

    @Data
    public static class ProcessItem {
        private Long processId;
        private String cardId;
        private String patientName;
        private String bodyPart;
        private String status;
    }

    @Data
    public static class ProcessRealtimeResponse {
        private String patientName;
        private String treatmentMode;
        private LocalDateTime startTime; // 添加进程开始时间
        private List<BodyPartTreatment> bodyParts;
    }

    @Data
    public static class BodyPartTreatment {
        private String bodyPart;
        private String remainingTime;
        private String intensity;
        private String elapsedTime; // 已用时间字符串格式
        private Long elapsedTimeSeconds; // 已用时间秒数
        private Long totalDurationSeconds; // 总时长秒数
        private Long remainingTimeSeconds; // 添加剩余时间秒数
    }

    @Data
    public static class PaginationInfo {
        private int currentPage;
        private int totalPages;
        private int totalRecords;
        private int pageSize;

        public PaginationInfo(int currentPage, int totalPages, int totalRecords, int pageSize) {
            this.currentPage = currentPage;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
            this.pageSize = pageSize;
        }
    }
}








